class MissingAttendance::RecordJob < MissingAttendance::BaseJob
  queue_as :default

  def perform(*args)
    return if skip_attendance_recording?

    active_enabled_accounts.find_each do |account|
      record_attendance_for_account(account)
    end
  end

  private

  def record_attendance_for_account(account)
    canvas_course_ids = account.courses
                     .active
                     .for_account(account)
                     .for_date(date)
                     .pluck(:canvas_id)


    canvas_course_ids.each_slice(100) do |batch|
      record_attendance_for_course_batch(batch)
    end
  end

  def record_attendance_for_course_batch(canvas_course_ids)
    missing = Enrollment
                .active
                .student
                .where(canvas_course_id: canvas_course_ids)
                .joins("LEFT OUTER JOIN attendances ON attendances.canvas_user_id = enrollments.canvas_user_id
                      AND attendances.canvas_course_id = enrollments.canvas_course_id
                      AND attendances.attendance_date = #{ActiveRecord::Base.connection.quote(date)}")
                .where('attendances.id IS NULL')
    return if missing.empty?

    Attendance.insert_all(
      missing.map do |enrollment|
        {
          canvas_user_id: enrollment.canvas_user_id,
          attendance_date: date,
          canvas_course_id: enrollment.canvas_course_id,
          created_at: Time.current,
          updated_at: Time.current
        }
      end,
      unique_by: [ :attendance_date, :canvas_course_id, :canvas_user_id ]
    )
  end
end
