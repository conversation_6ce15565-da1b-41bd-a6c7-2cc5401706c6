# frozen_string_literal: true

module OrganizationExtension
  extend ActiveSupport::Concern

  SHARD_OFFSET = 10_000_000_000_000

  included do
    # Runs every day 1 AM eastern time
    scheduled_task '0 6 * * *', :sync_canvas, worker: CanvasSyncStarterJob

    before_create do
      self.shard ||= Apartment.shard_configurations.keys.sample || 'default' unless Rails.env.test?
    end

    # Runs everyday at 2:30 hours
    scheduled_task '30 2 * * *', :record_missing_attendance, worker: MissingAttendance::RecordJob

    # Runs everyday at 5:30 hours
    scheduled_task '30 5 * * *', :queue_missing_attendance_notifications,
                   worker: MissingAttendance::QueueNotificationsJob

    # Runs every 4 hours
    # TODO: It is legacy importer designed to import csv files from SFTP server
    scheduled_task '0 */4 * * *', :sync_sftp_resources_csv, worker: SyncResourcesJob
    scheduled_task '0 */4 * * *', :sync_sftp_resources, worker: SyncDataFromSftpJob

    # Runs everyday from 11 AM to 7 PM every hour
    scheduled_task '0 11-23 * * *', :hosted_data_push, worker: HostedDataPushJob

    after_lti_install do
      settings[:canvas][:external_tool_id] = @new_lti_installation[:id]
    end
  end

  class_methods do
    def for_canvas_shard(cid)
      cid = (cid / SHARD_OFFSET).floor if cid > SHARD_OFFSET
      find_by(canvas_shard_id: cid)
    end
  end
end
