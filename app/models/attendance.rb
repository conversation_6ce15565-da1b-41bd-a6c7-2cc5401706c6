class Attendance < ApplicationRecord
  # TODO - This table looks like it will grow indefinitely. We should add a job to purge or archive records after
  # courses drop off
  attr_accessor :actioning_user

  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_course_id

  validates :attendance_date, presence: true
  validates :time_in_minutes, numericality: { only_integer: true, greater_than_or_equal_to: 0 }, allow_nil: true

  # Ensure that attendance record is unique per user, course, and date
  validates :attendance_date, uniqueness: { scope: [ :canvas_user_id, :canvas_course_id ],
                                            message: 'attendance for this user and course on this date already exists' }

  after_commit :record_value_change_audit_log, if: :attendance_value_changed?

  scope :missing, -> { where(time_in_minutes: nil) }

  def as_json(options = {})
    super(options).merge(
      'attendance_date' => attendance_date&.strftime('%d-%m-%Y')
    )
  end

  private

  def attendance_value_changed?
    # Not sure, but it seems like we don't want audit logs for records
    # that are just being initialized and have no time_in_minutes set.
    # Front end users cannot set time_in_minutes to nil.
    return false if time_in_minutes.nil?

    saved_change_to_time_in_minutes?
  end

  def record_value_change_audit_log
    AuditLog.create!(
      student_canvas_id: user.canvas_id,
      student_sis_id: user.sis_id,
      student_name: user.name,
      course_sis_id: course.sis_id,
      course_canvas_id: course.canvas_id,
      course_name: course.name,
      attendance_date: attendance_date,
      previous_value: time_in_minutes_before_last_save,
      updated_value: time_in_minutes,
      actioning_user_sis_id: actioning_user&.sis_id,
      actioning_user_canvas_id: actioning_user&.canvas_id,
      actioning_user_name: actioning_user&.name
    )
  end
end
