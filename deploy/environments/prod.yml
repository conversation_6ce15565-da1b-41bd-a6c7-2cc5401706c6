app:
  name: stride-learning-coach-attendance
  environment: prod
  vpc_cluster: insproserv-prod
  region: us-west-2

  alerting:
    type: datadog
    notify_groups:
      - '@pagerduty-proserv-cloudgate'
      - '@slack-proservices_errors'

  sso_roles:
    - ProServe-Engineers
    - ProServe-Admins

  load_balancers:
    web:
      ssl_cert_arn: "arn:aws:acm:us-west-2:376567524831:certificate/23c25656-1945-470a-ba73-aa6632a24a8f"
      tags:
        role: loadbalancer
        sub-role: web

  instance_pools:
    web:
      min_size: 4
      max_size: 8
    work:
      min_size: 3
      max_size: 6

  databases:
    learningcoachattendancepostgres:
      db_instance_class: db.m6g.large
      storage_type: gp3
      allocated_storage: 150
      engine_version: '17.6'
      tags:
        role: db
        sub-role: main

    shardalpha:
      db_instance_class: db.m6g.large
      storage_type: gp3
      allocated_storage: 150
      env_var_prefix: SHARD_DB_ALPHA
      master_username: app
      name: learning_coach_attendance_production
      engine_version: '17.6'
      tags:
        role: db
        sub-role: shard-alpha

    shardbravo:
      db_instance_class: db.m6g.large
      storage_type: gp3
      allocated_storage: 150
      env_var_prefix: SHARD_DB_BRAVO
      master_username: app
      name: learning_coach_attendance_production
      engine_version: '17.6'
      tags:
        role: db
        sub-role: shard-bravo

  env_vars:
    SUPPORT_TICKET_EMAIL_ADDRESS: '<EMAIL>'
    SENTRY_DSN: https://<EMAIL>/529
    SENTRY_ENVIRONMENT: STAGING
    RAILS_MAX_THREADS: "50"
  tags:
    Environment: staging
