app:
  name: stride-learning-coach-attendance
  environment: pdx-prod2
  vpc_cluster: insproserv-prod
  region: us-west-2

  alerting:
    type: datadog
    notify_groups:
      - '@pagerduty-proserv-cloudgate'
      - '@slack-proservices_errors'

  sso_roles:
    - ProServe-Engineers
    - ProServe-Admins

  load_balancers:
    web:
      ssl_cert_arn: "arn:aws:acm:us-west-2:376567524831:certificate/6649d789-e05d-4762-8b23-8fd7c15d1f4c"
      tags:
        role: loadbalancer
        sub-role: web

  instance_pools:
    web:
      instance_type: c6a.xlarge
      min_size: 8
      max_size: 16
      env_vars:
        PASSENGER_MAX_POOL_SIZE: '7'
        PASSENGER_MIN_INSTANCES: '7'
        PASSENGER_MAX_REQUESTS: '50000'
        PASSENGER_MAX_REQUEST_QUEUE_SIZE: '200'
    work:
      min_size: 4
      max_size: 8

  caches:
    redis:
      cache_node_type: cache.t3.medium
      env_var_prefix: REDIS
      num_cache_nodes: 1
      engine_version: '7.0'
      tags:
        role: redis

  databases:
    learningcoachattendancepostgres:
      db_instance_class: db.m5.large
      storage_type: gp2
      allocated_storage: 150
      tags:
        role: db
        sub-role: main

    shardalpha:
      db_instance_class: db.m5.4xlarge
      storage_type: gp2
      allocated_storage: 150
      env_var_prefix: SHARD_DB_ALPHA
      master_username: app
      name: learning_coach_attendance_production
      engine_version: '17.6'
      tags:
        role: db
        sub-role: shard-alpha

    shardbravo:
      db_instance_class: db.m5.4xlarge
      storage_type: gp2
      allocated_storage: 150
      env_var_prefix: SHARD_DB_BRAVO
      master_username: app
      name: learning_coach_attendance_production
      engine_version: '17.6'
      tags:
        role: db
        sub-role: shard-bravo

    shardcharlie:
      db_instance_class: db.m5.4xlarge
      storage_type: gp2
      allocated_storage: 150
      env_var_prefix: SHARD_DB_CHARLIE
      master_username: app
      name: learning_coach_attendance_production
      engine_version: '17.6'
      tags:
        role: db
        sub-role: shard-charlie

  env_vars:
    SUPPORT_TICKET_EMAIL_ADDRESS: '<EMAIL>'
    SENTRY_DSN: https://<EMAIL>/529
    SENTRY_ENVIRONMENT: PRODUCTION
    RAILS_MAX_THREADS: "50"
  tags:
    Environment: production
