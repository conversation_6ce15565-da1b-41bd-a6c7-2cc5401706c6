cloudgate:
  required_version: '~> 12.3'

app:
  name: stride-learning-coach-attendance

  user_defaults:
    deploy: false
    ssh: true
    trusted_aws_accounts:
      inscloudgate:
        disabled: true
      inseng:
        disabled: true

  tags:
    alert_group: custom-development
    team: cdev
    customer: stride
    platform: cloudgate

  users:
    mvalentine:
      email: <EMAIL>
      ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDNYJT78BQCqbsDQHi59QKxNk2BWXXooJfIgyjikWQ/PirWQAM8kC2+Fss5opXbqCDp0i/zeMlRMSyYmKOSGsUhEaCBRuYhYx1oL0dhFvgQP7xqilP6TaldEpZQO0QOWreRabZig5P6KOGt30+KcNXlsZ+odIY8h+HvIJg7PZUh/+Eq7ElE6NXYEGJWVtadSQYiJgcVYkQ1fABCtbOpFNo6lwHM58BiXyrnzbSMKEDTd/nBiUR9qoPeLSK0gosIZX/aW2gIfqoxVdmsKE6JtfCanwQZdZjCchoAZLWb/iAJ/zzsoz7y4yUosap32GuP1y5+tW5GiiEAMO6MrN9bBR7V
    brandonbr:
      email: <EMAIL>
      ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCzHSxAskoNF9krpnaYmuyLNWFU/tT5+i0741vMyfEBiF6hHX+kksbylsKhB0XRK9O+vHYF7idpUiczQDkABOCkus0MC9zcYklJQDT46tw4p8pE2kRnwJoMeS2Fpz5xrmjAIklc6UmghPnbfe1+wBfE2PasOrpiOM+66xgsKbIn1yr1XbuHIgVdlaIwaY8ssKIBupCKS4zguBSqXJZRcBNj6BuLKcwBhKYt1z+OKL7lHCbFQHb0CkAGrwicdMpLSUK9U7pmqOHWGYICdNvSE2c5IHoHklqtoUwTHhHB0iiledx0/GoZefKJwj2EELwYfnvIiIChuEUJor/RZHnJ9py/ <EMAIL>
    cmcguire:
      email: <EMAIL>
      ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC0FuzG1GWjK/KJHrPIM1QUAu4AsLjiydRaen/rEITUtwGEs5b1q6PDeRRM820YcnjpzlTmee4+rTLqD8h64XCyHz6fwsaF+o5yQjjH7GZuqomwEb/PANP5TNLiFt/HvsXAsjYZGN9xdOvSmcSKzZvFX14aIPLiHHNgi7wjOW0Q9LjBwBya0F3IGMpj/T9plmC7CGMrU+JS8Ilf769v85Mckh/7oR87TV3euC7T8RZq/UbtKz74WCnWN3Cn6FLOmpxNItAKrzFEhh6sY7pp1m96LFPSH3uV3cPY6YC3Iwd1wxv/tdIkNvWBMMtwf8zCQTt8hFlOPG9ngCPXlaD9xrOb <EMAIL>
    smikkelsen:
      email: <EMAIL>
      ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDQR1zjw/15rA+6q+5eYhAF+9+FlbYvmxsv/upAADGcU4ZdR1Cx5P9iQ46n/WfgX5yXXxTQNBxgK2wabaG1UubDrzH+Y5iuqMAkxaZWspIrpegQAmnSwxtclHeFFHPogQPmNUw3oMuTTneYyTmRCfaalBJt3E7nGSy/6dw6MPyO9gbSUnWf8V0WHN6kowEmsoQhnoRRgiPlw60eCHhuwrZgD7sCX5q2pUx6mdkipJTjrarTPzUNzqL/VYSWpidXDHWWvRuD6YqMXYuyC+hzl2qbgNjwlTNq0ysek1AE7rMl9bFpBWJpxH92AYa4FSF0od4wVOSKFJDKdr3wFr/tSPT1 <EMAIL>
    eknapp:
      email: <EMAIL>
      ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD19eZLlwimvuI5gg02bm+8HQCHM2g0XJEjgGTuW6T+OhyCQ8TPTfXPpyu/8q9QsQ+b1RPLvAqecagaX7RIoixf7jJpHfUpXBeqzM9jhOP+Z1N8SsT8pgmjuHHJjziHoL5f5CmcnrziA0MeIK70zoYbT19nUQpAWoh5xzlgJEHMjcDHJheLSPjdCyZOptkFGJeQvT8gn48KnKRO9qXTUdqdBfpu7MOVWRySfSiGGEQOEwlLJlcurckfEOYzAZCTrUQTtdZ3AEwQfsrnYU2WaT1WHibX3iS52hOqmvzG7QzCCIilNAw3p/G/eLk816FdZh0KGmmpBgJpKrNB/LTpV6JV <EMAIL>
    hitesh.rawal:
      email: <EMAIL>
      ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDJ9qV8bZpNsZFmvs0lwgAEKJIU8vx0HiZ8+AnB8CiND/Gd17HtczOeAMOAqPvQ2GB+zIfh7SbAhcWYnfSbGkIk2rssIeWemfOc+xnQ6xBf8ZykmlohzcJFg1QjZkq28bZRb/ibD97srk+YeUKbCwE8xQdcdssKP78qHmDJ3/tzeCZdC1xbqvbjaNug522N7aDZDyIka1PN5ZUk8EgMBOp7Ue4ZUoma7+gn7wXecJvAdxk0A3rJhQJqJRjxare6zg9GCx8Lwf1YCkeOxTqbxqc03tWdWRJiPVRubF4NWXBvtYgWrYOT5D0SQLxx8wsdqVNUBvgSzcCMmDbyx1x7+s/v <EMAIL>
    mukesh.patel:
      email: <EMAIL>
      ssh_key: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINKkJuEUJpZ2w6Ou5sN5/e058ngodaxSIzB0npuf7SGr <EMAIL>
    chetan.dhatrak:
      email: <EMAIL>
      ssh_key: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGQPFlI57CSeQmPNu69NK6rpfkWWNUwTkAvoCxr9nB2P <EMAIL>
    vamsi.krishana:
      email: <EMAIL>
      ssh_key: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAILcztnpZwwWqMTUJooea0SrPCcYuMVQsZvZJUtuwDDzT <EMAIL>

  instance_pools:
    web:
      instance_type: t3.medium
      http_port: 8080
      https_port: 8443
      health_check_path: '/health_check'
      min_size: 1
      max_size: 1
      env_vars:
        LEGACY_PORTS: '0'
        RAILS_ENV: 'production'
        PASSENGER_MAX_POOL_SIZE: '3'
        PASSENGER_MIN_INSTANCES: '3'
        PASSENGER_MAX_REQUESTS: '50000'
        PASSENGER_MAX_REQUEST_QUEUE_SIZE: '100'
      tags:
        role: web
    work:
      instance_type: t3.medium
      min_size: 1
      max_size: 1
      env_vars:
        RAILS_ENV: 'production'
      hooks:
        post:
          - command: bundle exec rails db:migrate
      tags:
        role: work
  env_vars:
    RAILS_ENV: 'production'
    # SENTRY_DSN:
    SMTP_ADDRESS: 'email-smtp.us-east-1.amazonaws.com'
    SMTP_USERNAME: 'AKIAUPKAAFAEYL2XZTNC'

  caches:
    redis:
      cache_node_type: cache.t3.medium
      env_var_prefix: REDIS
      num_cache_nodes: 1
      engine_version: '8.2.1'
      tags:
        role: redis

  databases:
    learningcoachattendancepostgres:
      engine_version: '17.6'
      env_var_prefix: DATABASE
      master_username: learningcoachattendanceuser
      db_instance_class: db.t3.medium
      storage_type: gp3
      allocated_storage: 20

  s3_buckets:
    reports:
      env_var: S3_BUCKET_NAME
      tags:
        role: s3

  sends_email: true
