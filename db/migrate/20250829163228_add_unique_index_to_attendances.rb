class AddUniqueIndexToAttendances < ActiveRecord::Migration[7.0]
  disable_ddl_transaction!

  def change
    remove_index :attendances,
              column: [ :attendance_date, :canvas_course_id, :canvas_user_id ],
              name: "ix_attendances_course_date_user",
              algorithm: :concurrently

    add_index :attendances,
              [ :attendance_date, :canvas_course_id, :canvas_user_id ],
              unique: true,
              algorithm: :concurrently,
              name: "ix_attendances_course_date_user_unique"
  end
end
