class AddIndexToAttendancesForMissingAttendance < ActiveRecord::Migration[7.0]
  disable_ddl_transaction! # This is because concurrent index creation cannot run inside a transaction, and Rails
  # migrations run in a transaction by default. Disabling the transaction is required for the migration to succeed.
  def change
    add_index :attendances,
              [ :attendance_date, :canvas_course_id, :canvas_user_id ],
              name: :ix_attendances_course_date_user,
              algorithm: :concurrently
  end
end
