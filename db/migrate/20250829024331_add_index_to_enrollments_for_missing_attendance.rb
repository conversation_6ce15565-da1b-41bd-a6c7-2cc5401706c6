class AddIndexToEnrollmentsForMissingAttendance < ActiveRecord::Migration[7.0]
  disable_ddl_transaction! # This is because concurrent index creation cannot run inside a transaction, and Rails
  # migrations run in a transaction by default. Disabling the transaction is required for the migration to succeed.
  def change
    add_index :enrollments,
              [ :canvas_course_id, :workflow_state, :base_role_type, :canvas_user_id ],
              name: :idx_enrollments_on_course_state_role_user,
              algorithm: :concurrently
  end
end
