# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_08_29_163228) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "accounts", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "sis_id"
    t.bigint "canvas_parent_account_id"
    t.string "sis_parent_account_id"
    t.string "name"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "school_year"
    t.boolean "enabled", default: true
    t.integer "lockout_date_setting", default: 0
    t.date "lockout_date"
    t.integer "lockout_days", default: 14
    t.integer "default_time_to_complete"
    t.index ["canvas_id"], name: "index_accounts_on_canvas_id", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admins", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "role_name"
    t.bigint "canvas_account_id"
    t.bigint "canvas_role_id"
    t.bigint "canvas_user_id"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_account_id"], name: "index_admins_on_canvas_account_id"
    t.index ["canvas_id"], name: "index_admins_on_canvas_id", unique: true
    t.index ["canvas_role_id"], name: "index_admins_on_canvas_role_id"
    t.index ["canvas_user_id"], name: "index_admins_on_canvas_user_id"
  end

  create_table "api_keys", force: :cascade do |t|
    t.bigint "canvas_user_id"
    t.string "name", null: false
    t.string "token", null: false
    t.string "token_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["canvas_user_id"], name: "index_api_keys_on_canvas_user_id"
    t.index ["token"], name: "index_api_keys_on_token", unique: true
  end

  create_table "attendances", force: :cascade do |t|
    t.bigint "canvas_user_id"
    t.bigint "canvas_course_id"
    t.date "attendance_date", null: false
    t.integer "time_in_minutes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attendance_date", "canvas_course_id", "canvas_user_id"], name: "ix_attendances_course_date_user_unique", unique: true
  end

  create_table "audit_logs", force: :cascade do |t|
    t.bigint "student_canvas_id"
    t.string "student_sis_id"
    t.string "student_name"
    t.string "course_sis_id"
    t.bigint "course_canvas_id"
    t.string "course_name"
    t.date "attendance_date"
    t.integer "previous_value"
    t.integer "updated_value"
    t.string "actioning_user_sis_id"
    t.bigint "actioning_user_canvas_id"
    t.string "actioning_user_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "canvas_sync_job_logs", id: :serial, force: :cascade do |t|
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.string "exception"
    t.text "backtrace"
    t.string "job_class"
    t.string "status"
    t.text "metadata"
    t.text "job_arguments"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "job_id"
    t.integer "fork_count"
    t.index ["job_id"], name: "index_canvas_sync_job_logs_on_job_id"
  end

  create_table "canvas_sync_sync_batches", id: :serial, force: :cascade do |t|
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.string "status"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.boolean "full_sync", default: false
    t.string "batch_genre"
    t.string "batch_bid"
  end

  create_table "courses", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "sis_id"
    t.string "course_code"
    t.string "name"
    t.string "workflow_state"
    t.integer "canvas_account_id"
    t.integer "canvas_term_id"
    t.datetime "start_at", precision: nil
    t.datetime "end_at", precision: nil
    t.bigint "grading_standard_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "is_blueprint", default: false
    t.bigint "canvas_blueprint_course_id"
    t.integer "default_time_to_complete", default: 60
    t.date "resolved_start_date"
    t.date "resolved_end_date"
    t.string "grade_level"
    t.string "subject"
    t.index ["canvas_id"], name: "index_courses_on_canvas_id", unique: true
  end

  create_table "enrollments", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_course_id"
    t.string "course_sis_id"
    t.bigint "canvas_user_id"
    t.string "user_sis_id"
    t.string "role"
    t.bigint "canvas_role_id"
    t.bigint "canvas_section_id"
    t.string "workflow_state"
    t.string "base_role_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "canvas_associated_user_id"
    t.index ["canvas_course_id", "workflow_state", "base_role_type", "canvas_user_id"], name: "idx_enrollments_on_course_state_role_user"
    t.index ["canvas_course_id"], name: "index_enrollments_on_canvas_course_id"
    t.index ["canvas_id"], name: "index_enrollments_on_canvas_id", unique: true
    t.index ["canvas_user_id"], name: "index_enrollments_on_canvas_user_id"
  end

  create_table "exception_dates", force: :cascade do |t|
    t.date "date", null: false
    t.string "name", null: false
    t.integer "exception_type", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "imports", force: :cascade do |t|
    t.integer "status", default: 0
    t.integer "records_updated"
    t.text "error_records"
    t.datetime "completed_at"
    t.string "error_message"
    t.bigint "uploaded_by_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "inst_data_shipper_dump_batches", id: :serial, force: :cascade do |t|
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.string "status"
    t.string "job_class"
    t.string "genre"
    t.string "batch_id"
    t.string "exception"
    t.text "backtrace"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "panda_pal_api_calls", id: :serial, force: :cascade do |t|
    t.text "logic"
    t.string "expiration"
    t.integer "uses_remaining"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "panda_pal_organizations", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "key"
    t.string "secret"
    t.string "canvas_account_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "salesforce_id"
    t.text "encrypted_settings"
    t.string "encrypted_settings_iv"
    t.string "shard"
    t.integer "canvas_shard_id"
    t.index ["key"], name: "index_panda_pal_organizations_on_key", unique: true
    t.index ["name"], name: "index_panda_pal_organizations_on_name", unique: true
  end

  create_table "panda_pal_sessions", id: :serial, force: :cascade do |t|
    t.string "session_key"
    t.text "data"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "panda_pal_organization_id"
    t.index ["panda_pal_organization_id"], name: "index_panda_pal_sessions_on_panda_pal_organization_id"
    t.index ["session_key"], name: "index_panda_pal_sessions_on_session_key", unique: true
  end

  create_table "pseudonyms", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_user_id"
    t.string "sis_id"
    t.string "unique_id"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_id"], name: "index_pseudonyms_on_canvas_id", unique: true
    t.index ["canvas_user_id"], name: "index_pseudonyms_on_canvas_user_id"
  end

  create_table "reports", force: :cascade do |t|
    t.integer "status", default: 0
    t.bigint "created_by_user_id"
    t.string "error_message"
    t.datetime "completed_at"
    t.text "report_arguments"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "roles", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "label"
    t.string "base_role_type"
    t.bigint "canvas_account_id"
    t.string "workflow_state"
    t.json "permissions"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_account_id"], name: "index_roles_on_canvas_account_id"
    t.index ["canvas_id"], name: "index_roles_on_canvas_id", unique: true
  end

  create_table "terms", force: :cascade do |t|
    t.integer "canvas_id", null: false
    t.string "name"
    t.datetime "start_at", precision: nil
    t.datetime "end_at", precision: nil
    t.string "workflow_state"
    t.integer "grading_period_group_id"
    t.string "sis_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_id"], name: "index_terms_on_canvas_id", unique: true
  end

  create_table "user_observers", force: :cascade do |t|
    t.bigint "observing_user_id"
    t.bigint "observed_user_id"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["observed_user_id", "observing_user_id"], name: "index_user_observers_on_observed_user_id_and_observing_user_id", unique: true
    t.index ["observed_user_id"], name: "index_user_observers_on_observed_user_id"
    t.index ["observing_user_id"], name: "index_user_observers_on_observing_user_id"
  end

  create_table "user_shard_associations", force: :cascade do |t|
    t.bigint "canvas_user_id"
    t.integer "canvas_shard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["canvas_user_id", "canvas_shard_id"], name: "shard_assoc_uniqueness", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.string "workflow_state"
    t.string "login_id"
    t.string "name"
    t.string "sortable_name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "grade_level"
    t.text "contact_information"
    t.bigint "primary_school_id"
    t.string "sis_id"
    t.text "contact_information2"
    t.index ["canvas_id"], name: "index_users_on_canvas_id", unique: true
    t.index ["primary_school_id"], name: "index_users_on_primary_school_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
end
