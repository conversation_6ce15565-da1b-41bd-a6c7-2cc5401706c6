# Debug
params =ActionController::Parameters.new({"date":"28-08-2025","courses":[{"canvas_id":55055,"name":"ART020A Music Appreciation - Wintrow","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":71210,"name":"CS Learning Strategies and Support - PBIS","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":55041,"name":"ENG303ADE4 American Literature - Conner","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":54976,"name":"HST403DE3 US Government and Politics - Watson","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":55031,"name":"MTH307AE2 Practical Math - Parrill","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":61134,"name":"ORN010E6 Online Learning 6-12 - Merchant","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":53769,"name":"Post Secondary Coursework (Att) - Teacher","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":55349,"name":"SCI303AD Chemistry - Strayer","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":60896,"name":"Support - Halpin","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"},{"canvas_id":60365,"name":"TCH150ADE2 Found of Cybersecurity TX - Mullins","time_in_minutes":"60","organization_name":"k12learning1","enrollment_canvas_user_id":"58831"}],"organization_id":"2","user_id":"58831","attendance":{}})
grouped_organization_data = params[:courses].group_by { |p| p[:organization_name] }

grouped_organization_data = params[:courses].group_by { |p| p[:organization_name] }

grouped_organization_data.each do |org_name, org_params|
  PandaPal::Organization.find_by(name: org_name).switch_tenant do
    # Considering the attendance record is just for a single canvas user in that grouped organization data
    canvas_user_id = org_params.first[:enrollment_canvas_user_id]

    student = User.find_by_canvas_id(canvas_user_id)

    courses = student.courses
      .active_student_enrollments
      .where(canvas_id: org_params.pluck(:canvas_id))

    courses_canvas_ids = courses.pluck(:canvas_id)

    attendance_data = org_params.select { |c| c[:canvas_id].to_i.in?(courses_canvas_ids) }

    attendance_data.each do |attendance_info|
      attendance = Attendance.find_or_initialize_by(
        user: student,
        canvas_course_id: attendance_info[:canvas_id],
        attendance_date: params[:date].to_date
      )
      # attendance.actioning_user = current_user
      attendance.time_in_minutes = attendance_info[:time_in_minutes]
      next if attendance.valid?
      pp attendance.errors
      pp attendance
    end
  end
end;nil

# Count
pp Attendance
  .group(:canvas_user_id, :canvas_course_id, :attendance_date)
  .having("COUNT(*) > 2")
  .count;nil

# Enrollment Count
Enrollment.where(
  canvas_user_id: 308246,
  canvas_course_id: 65563
).count


PandaPal::Organization.all.each do |org|
  org.switch_tenant
  Attendance.where(time_in_minutes: nil)
    .select(:canvas_user_id, :canvas_course_id, :attendance_date)
    .group(:canvas_user_id, :canvas_course_id, :attendance_date)
    .having("COUNT(*) > 1")
    .each do |dup|
      pp dup
      duplicates = Attendance.where(
        canvas_user_id: dup.canvas_user_id,
        canvas_course_id: dup.canvas_course_id,
        attendance_date: dup.attendance_date,
        time_in_minutes: nil
      ).order(:id)

      duplicates.ci
      # keep the first one, delete the rest
      duplicates.offset(1).count
  end;nil
end
