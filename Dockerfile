FROM instructure/ruby-passenger:3.3

USER root

RUN apt-get update \
  && apt-get install -y git build-essential postgresql-client
RUN curl -sL https://deb.nodesource.com/setup_22.x | bash - && apt-get install -y nodejs

ENV APP_HOME=/usr/src/app/ \
    RAILS_ENV=production \
    NODE_ENV=production \
    NODE_OPTIONS="--max-old-space-size=2048"

RUN mkdir -p $APP_HOME \
    && npm install -g yarn@1.22.22

WORKDIR $APP_HOME

# Install Gems
COPY Gemfile $APP_HOME/Gemfile
COPY Gemfile.lock $APP_HOME/Gemfile.lock
RUN bundle config set --local system 'true' \
  && bundle config set --local without 'development'
RUN gem install bundler:2.5.21
RUN bundle _2.5.21_ install

# Switch to Runtime User
RUN chown -R docker:docker $APP_HOME
USER docker

# Install NPM Packages
COPY --chown=docker:docker package.json yarn.lock $APP_HOME/
RUN yarn install

# Install the App itself
ADD --chown=docker:docker . $APP_HOME

# NOTE: This is a fake key. A PRODUCTION_KEY1 is required by symmetric-encryption in order to boot up. This is encrypted
# with cg vault, but those environment variables are not loaded into the environment at this point, so I've just placed
# a fake key here instead, so that the precompile can actually take place. This key shouldn't be used for any actual production
# usage.
RUN \
  SECRET_KEY_BASE=a-fake-secret-key \
  bundle exec rake assets:precompile

CMD ["bin/startup"]
